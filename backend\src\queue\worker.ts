import { Job } from 'bullmq';
import { ExecuteFlowRequest, ExecutionLog } from '@rpa-project/shared';
import { FlowService } from '../services/flowService';
import { ExecutionService } from '../services/executionService';
import { getRunnerFactory } from '../runners/factory';
import { FlowExecutor } from '../runners/FlowExecutor';

export interface FlowJobData extends ExecuteFlowRequest {
  executionId?: string;
}

export class FlowExecutionWorker {
  private flowService = new FlowService();
  private executionService = new ExecutionService();

  async processJob(job: Job<FlowJobData>): Promise<any> {
    const { flowId, variables = {}, executionId } = job.data;

    console.log(`🚀 Processing flow execution job: ${job.id}`);
    console.log(`📋 Job data:`, { flowId, executionId, variables });

    try {
      // Update job progress
      await job.updateProgress(10);

      // Get flow definition
      console.log(`🔍 Looking up flow: ${flowId}`);
      const flow = await this.flowService.getFlowById(flowId);
      if (!flow) {
        console.error(`❌ Flow not found: ${flowId}`);
        throw new Error(`Flow not found: ${flowId}`);
      }
      console.log(`✅ Found flow: ${flow.name} (ID: ${flow.id})`);

      await job.updateProgress(20);

      // Find or create execution record
      let execution;
      if (executionId) {
        execution = await this.executionService.getExecutionById(executionId);
        if (!execution) {
          throw new Error(`Execution not found: ${executionId}`);
        }

        // Check if execution was cancelled - if so, don't process
        if (execution.status === 'cancelled') {
          console.log(`🛑 Execution ${executionId} was cancelled, skipping processing`);
          await this.executionService.addExecutionLog(executionId, {
            level: 'info',
            message: 'Execution was cancelled, skipping processing'
          });

          // Return success to prevent retry
          return {
            executionId: execution.id,
            status: 'cancelled',
            message: 'Execution was cancelled'
          };
        }

        // Check if execution is already completed - don't retry completed executions
        if (execution.status === 'completed') {
          console.log(`⚠️ Execution ${executionId} already completed, skipping processing`);
          return {
            executionId: execution.id,
            status: execution.status,
            message: `Execution already completed`
          };
        }

        // Allow retry of failed executions - log retry attempt
        if (execution.status === 'failed') {
          console.log(`🔄 Retrying failed execution: ${executionId}`);
          await this.executionService.addExecutionLog(executionId, {
            level: 'info',
            message: 'Retrying failed execution - starting new attempt'
          });
        }
      } else {
        execution = await this.executionService.createExecution(flowId, variables);
      }

      // Update execution status to running (this clears any previous error message)
      await this.executionService.updateExecutionStatus(execution.id, 'running');
      await job.updateProgress(30);

      // Initialize Flow Executor with multi-runner support
      const executor = new FlowExecutor(
        execution.id,
        async (log: Omit<ExecutionLog, 'timestamp'>) => {
          await this.executionService.addExecutionLog(execution.id, log);
        },
        async () => {
          const currentExecution = await this.executionService.getExecutionById(execution.id);
          return currentExecution?.status === 'cancelled';
        }
      );

      await job.updateProgress(40);

      // Execute the flow using the multi-runner system
      console.log(`▶️ Executing flow: ${flow.name}`);
      let results;

      try {
        results = await executor.executeFlow(flow, variables);
      } finally {
        // Ensure cleanup happens even if execution fails
        try {
          await executor.cleanup();
        } catch (cleanupError) {
          console.error(`⚠️ Error during executor cleanup: ${cleanupError instanceof Error ? cleanupError.message : 'Unknown error'}`);
        }
      }

      await job.updateProgress(90);

      // Update execution with results
      await this.executionService.updateExecutionResults(execution.id, results);
      await this.executionService.updateExecutionStatus(execution.id, 'completed');

      await job.updateProgress(100);

      console.log(`✅ Flow execution completed: ${execution.id}`);

      return {
        executionId: execution.id,
        status: 'completed',
        results
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : String(error);

      console.error(`❌ Flow execution failed: ${errorMessage}`);

      // Check if execution was cancelled during processing
      if (executionId) {
        try {
          const currentExecution = await this.executionService.getExecutionById(executionId);
          if (currentExecution?.status === 'cancelled') {
            console.log(`🛑 Execution ${executionId} was cancelled during processing, not retrying`);
            await this.executionService.addExecutionLog(executionId, {
              level: 'info',
              message: 'Execution was cancelled during processing'
            });

            // Return success to prevent retry
            return {
              executionId: executionId,
              status: 'cancelled',
              message: 'Execution was cancelled during processing'
            };
          }

          // Update execution status to failed only if not cancelled
          await this.executionService.updateExecutionStatus(
            executionId,
            'failed',
            errorMessage
          );
          await this.executionService.addExecutionLog(executionId, {
            level: 'error',
            message: `Execution failed: ${errorMessage}`,
            data: { error: errorStack }
          });
        } catch (logError) {
          console.error('Failed to update execution status:', logError);
        }
      }

      throw error;
    }
  }
}
