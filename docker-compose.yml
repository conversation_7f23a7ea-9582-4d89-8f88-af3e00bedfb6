# Load environment variables from .env.docker file
# Copy .env.docker.example to .env.docker and update values
services:
  redis:
    image: redis:7-alpine
    container_name: rpa-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy noeviction
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - rpa-network

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: rpa-backend
    ports:
      - "3002:3002"
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - PORT=3002
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./screenshots:/app/screenshots
      - ./downloads:/app/downloads
      - ./backend/data:/app/data
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - rpa-network

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: rpa-frontend
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - rpa-network

volumes:
  redis_data:
    driver: local

networks:
  rpa-network:
    driver: bridge
